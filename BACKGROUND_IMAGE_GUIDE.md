# Background Image Feature Guide

## Overview
The Dakoii Provincial Government Theme now supports custom background images through the WordPress Customizer. This feature allows you to upload and display custom background images while maintaining the beautiful cultural gradient as a fallback.

## How to Upload and Set Background Images

### Method 1: WordPress Customizer (Recommended)
1. **Access the Customizer**
   - Go to your WordPress admin dashboard
   - Navigate to `Appearance > Customize`

2. **Find Background Image Settings**
   - Look for the "Background Image" section in the customizer
   - Click to expand it

3. **Upload Your Image**
   - Click "Select Image" or "Change Image"
   - Choose an image from your media library or upload a new one
   - For best results, use high-resolution images (1920x1080 or larger)

4. **Configure Image Settings**
   - **Repeat**: Choose how the image repeats (No Repeat recommended for full images)
   - **Position**: Set horizontal and vertical positioning (Center/Center recommended)
   - **Size**: Choose how the image scales (Cover recommended for full-screen backgrounds)
   - **Attachment**: Choose Fixed for images that don't scroll with content

5. **Preview and Publish**
   - Preview your changes in the customizer
   - Click "Publish" when satisfied

### Method 2: Background Color Only
1. In the same "Background Image" section
2. Set a background color using the color picker
3. This will override the default gradient with a solid color

## Technical Details

### What's Fixed
- **Issue**: Previously uploaded background images showed "failed to load image data"
- **Cause**: CSS gradient was overriding WordPress custom background functionality
- **Solution**: Dynamic CSS output that respects custom backgrounds

### How It Works
1. **Custom Background Image**: When uploaded, displays with your chosen settings
2. **Custom Background Color**: When set (without image), displays as solid color
3. **Default Gradient**: When no custom background is set, shows the cultural PNG flag gradient

### CSS Priority
The background system works in this order:
1. Custom background image (highest priority)
2. Custom background color (medium priority)  
3. Cultural gradient (fallback/default)

## Best Practices

### Image Recommendations
- **Resolution**: 1920x1080 or higher for full-screen backgrounds
- **File Size**: Optimize images to under 500KB for faster loading
- **Format**: JPG for photos, PNG for graphics with transparency
- **Aspect Ratio**: 16:9 works well for most screen sizes

### Design Considerations
- Ensure text remains readable over your background image
- Consider using semi-transparent overlays for better text contrast
- Test on different screen sizes and devices
- Keep cultural significance in mind for government sites

### Performance Tips
- Compress images before uploading
- Use appropriate image formats (JPG vs PNG)
- Consider mobile users with slower connections
- Test loading times after setting background images

## Troubleshooting

### Image Not Displaying
1. Check if the image uploaded successfully in Media Library
2. Verify the image URL is accessible
3. Clear any caching plugins
4. Check browser developer tools for CSS conflicts

### Image Quality Issues
1. Upload higher resolution images
2. Check image compression settings
3. Ensure proper aspect ratio for your layout

### Performance Issues
1. Optimize image file size
2. Use appropriate image formats
3. Consider using CSS gradients for simple backgrounds

## Cultural Considerations
When choosing background images for the Dakoii Provincial Government Theme:
- Respect PNG cultural heritage and East Sepik traditions
- Use images that complement the PNG flag colors (red, green, yellow)
- Consider traditional patterns and cultural significance
- Maintain professional appearance for government sites

## Support
If you encounter issues with background images:
1. Check this guide first
2. Verify WordPress and theme are up to date
3. Test with default WordPress themes to isolate issues
4. Contact theme support with specific error messages
